import React, { useCallback, useEffect,  useRef,  } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditor<PERSON>tem<PERSON>tom } from '@repo/shared/lib/atoms/editor-atoms'

import MainGame, { useGameState } from './GameMain'
import {  useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { useRewards, useRoundReward } from '@repo/shared/lib/rewards/index'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { UiWidgetContainer } from '@repo/shared-game-utils/components/UiWidgetContainer'

export const StartScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config,  resolveAssetUrl } = useGame<ReactGameConfig>()

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.startScreenOverlay}
                dataConfigKey="startScreenOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.startScreenTitle}
                    dataConfigKey="startScreenTitle"
                    className="mb-6"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.startScreenStartButton}
                    dataConfigKey="startScreenStartButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}

// Lose Life Screen - Shows the message when player loses a life
export const TryAgainScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)


    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <UiWidgetContainer  widgetId={'tryAgain'} />

                <GameButton
                    config={config.tryAgainButton}
                    dataConfigKey="tryAgainButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
        </div>
    )
}



// Game Over Screen - Shows when player reached the target score
export const GameOverScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
             <UiWidgetContainer  widgetId={'gameOver'} />
        </div>
    )
}

// Reward Screen - Shows the reward screen when rewards are enabled
export const RewardScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl, setCurrentScreenId, isPreview } = useGame<ReactGameConfig>()
    const { roundId } = useGameState()
    const { rewardRollResult } = useRoundReward(roundId)

    // In the new system, the reward result is already available from the round
    // No need to call pickReward again - the reward was already rolled

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
              <UiWidgetContainer  widgetId={'rewardScreen'} />
        </div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
               <UiWidgetContainer  widgetId={'outOfLives'} />
        </div>
    )
}



export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
    children?: any
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl, children }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)
    const initialGameScreenChecked = useRef(false)

    const currentScreen = selectedScreen ?? 'main'

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any} 
            initialGameScreenChecked={initialGameScreenChecked} 
            children={children}
            setCurrentScreenId={() => {}} defaultConfig={defaultGameConfig} />
    )
}
